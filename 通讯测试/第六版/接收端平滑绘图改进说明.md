# 接收端平滑绘图改进说明

## 问题描述

您观察到的锯齿状信号问题确实存在，主要原因是：

1. **绘图方式不当**：原来按采样点索引绘图，忽略了真实时间信息
2. **采样不均匀**：串口传输存在延迟和抖动，实际采样间隔不均匀
3. **时间信息丢失**：虽然记录了时间戳，但绘图时没有使用

## 问题分析

### 原始绘图方法的问题

```python
# 旧方法：按采样点索引绘图
ax.plot(data, label=name)  # X轴是0,1,2,3...采样点索引
```

**问题：**
- 假设采样是均匀的（实际不是）
- 忽略了真实的时间间隔变化
- 导致信号看起来有锯齿或不平滑

### 实际采样特性

根据测试数据显示：
- 理想采样间隔：50ms
- 实际平均间隔：52.9ms
- 标准差：6.2ms
- 变化范围：42.4ms - 64.4ms

这种不均匀性会导致按索引绘图时出现锯齿效果。

## 解决方案

### 1. 添加时间轴记录

```python
# 新增时间历史记录
self.time_history = []
self.start_time = None

def _update_data_history(self, parsed_data):
    # 记录真实时间
    current_time = time.time()
    if self.start_time is None:
        self.start_time = current_time
    
    relative_time = current_time - self.start_time
    self.time_history.append(relative_time)
```

### 2. 改进绘图方法

```python
# 新方法：使用真实时间轴绘图
def _update_plot_data(self):
    time_data = self.receiver.time_history
    
    for name in selected_fields:
        data = self.receiver.data_history.get(name, [])
        if data and len(data) == len(time_data):
            # 关键改进：使用时间轴而不是索引
            ax.plot(time_data, data, label=name, 
                   linewidth=1.5, marker='o', markersize=2, alpha=0.8)
    
    ax.set_xlabel("时间 (秒)")  # 而不是"采样点"
```

### 3. 视觉效果改进

- **线条样式**：增加线宽和透明度，更平滑的视觉效果
- **标记点**：小的圆点标记实际采样点
- **网格**：半透明网格便于读数
- **时间轴**：真实的时间单位（秒）

## 改进效果

### 1. 信号平滑性

**修改前：**
- 锯齿状外观
- 不能反映真实频率
- 时间信息丢失

**修改后：**
- 平滑的曲线
- 正确的时间特性
- 真实的信号形状

### 2. 时间准确性

**修改前：**
```
X轴: 0, 1, 2, 3, 4, 5... (采样点索引)
实际时间: 不可知
```

**修改后：**
```
X轴: 0.000, 0.053, 0.105, 0.159... (真实时间/秒)
实际时间: 准确反映
```

### 3. 分析能力

- **频率分析**：可以准确测量信号频率
- **相位关系**：多信号间的相位关系正确
- **时序分析**：事件的真实时间顺序

## 实际应用效果

### 正弦波信号
- **修改前**：可能看起来像锯齿波或方波
- **修改后**：显示为平滑的正弦曲线

### 阶跃信号
- **修改前**：跳变时间点不准确
- **修改后**：精确显示跳变时刻

### 复合信号
- **修改前**：各分量关系混乱
- **修改后**：正确显示各分量的时间关系

## 性能考虑

### 计算开销
- 时间记录：几乎无开销
- 绘图改进：开销相同（只是改变X轴数据）
- 内存使用：增加一个时间数组（可忽略）

### 兼容性
- 完全向后兼容
- 不影响数据保存功能
- 不影响协议解析

## 使用建议

1. **观察连续信号**：正弦波、三角波等会显示得更平滑
2. **时序分析**：可以准确测量事件间的时间间隔
3. **频率测量**：通过观察周期可以验证信号频率
4. **系统调试**：更容易发现时序相关的问题

## 总结

这个改进解决了您观察到的锯齿状信号问题：

1. **根本原因**：使用真实时间轴而不是采样点索引
2. **视觉效果**：信号显示更平滑、更真实
3. **分析价值**：提供准确的时间和频率信息
4. **实用性**：便于系统调试和信号分析

修改后的接收端会显示更加平滑和准确的信号波形，特别是对于连续信号（如正弦波）的改善效果会非常明显。
