#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试接收端平滑绘图的效果
模拟不均匀采样的数据，对比旧方法和新方法的绘图效果
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import math

def generate_test_data():
    """生成测试数据：模拟不均匀采样的正弦波"""
    # 模拟真实的采样时间（不均匀）
    base_interval = 0.05  # 基础采样间隔50ms
    num_samples = 100
    
    # 生成不均匀的时间点
    time_points = []
    current_time = 0
    
    for i in range(num_samples):
        # 添加随机延迟，模拟真实的串口传输延迟
        jitter = np.random.uniform(-0.01, 0.01)  # ±10ms的抖动
        processing_delay = np.random.uniform(0.001, 0.005)  # 1-5ms的处理延迟
        
        current_time += base_interval + jitter + processing_delay
        time_points.append(current_time)
    
    # 生成对应的信号值（1Hz正弦波）
    signal_values = []
    for t in time_points:
        value = 1000 * math.sin(2 * math.pi * 1.0 * t)  # 1Hz, 振幅1000
        signal_values.append(value)
    
    return time_points, signal_values

def plot_comparison():
    """对比旧方法和新方法的绘图效果"""
    time_points, signal_values = generate_test_data()
    
    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('接收端绘图方法对比', fontsize=16)
    
    # 子图1：旧方法 - 按采样点索引绘图
    axes[0, 0].plot(signal_values, 'b-o', markersize=3, linewidth=1)
    axes[0, 0].set_title('旧方法：按采样点索引绘图')
    axes[0, 0].set_xlabel('采样点索引')
    axes[0, 0].set_ylabel('信号值')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 子图2：新方法 - 按真实时间轴绘图
    axes[0, 1].plot(time_points, signal_values, 'r-o', markersize=3, linewidth=1)
    axes[0, 1].set_title('新方法：按真实时间轴绘图')
    axes[0, 1].set_xlabel('时间 (秒)')
    axes[0, 1].set_ylabel('信号值')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 子图3：时间间隔分析
    time_intervals = [time_points[i+1] - time_points[i] for i in range(len(time_points)-1)]
    axes[1, 0].plot(time_intervals, 'g-o', markersize=3)
    axes[1, 0].axhline(y=0.05, color='k', linestyle='--', alpha=0.5, label='理想间隔(50ms)')
    axes[1, 0].set_title('实际采样时间间隔')
    axes[1, 0].set_xlabel('采样点')
    axes[1, 0].set_ylabel('时间间隔 (秒)')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].legend()
    
    # 子图4：理想信号对比
    # 生成理想的均匀采样信号用于对比
    ideal_time = np.linspace(0, time_points[-1], len(time_points))
    ideal_signal = [1000 * math.sin(2 * math.pi * 1.0 * t) for t in ideal_time]
    
    axes[1, 1].plot(ideal_time, ideal_signal, 'k-', linewidth=2, alpha=0.7, label='理想信号')
    axes[1, 1].plot(time_points, signal_values, 'r-o', markersize=2, linewidth=1, alpha=0.8, label='实际采样')
    axes[1, 1].set_title('理想信号 vs 实际采样')
    axes[1, 1].set_xlabel('时间 (秒)')
    axes[1, 1].set_ylabel('信号值')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.show()
    
    # 打印统计信息
    print("=== 采样统计信息 ===")
    print(f"总采样点数: {len(time_points)}")
    print(f"总时间: {time_points[-1]:.3f}秒")
    print(f"平均采样间隔: {np.mean(time_intervals):.6f}秒")
    print(f"采样间隔标准差: {np.std(time_intervals):.6f}秒")
    print(f"最小间隔: {min(time_intervals):.6f}秒")
    print(f"最大间隔: {max(time_intervals):.6f}秒")
    
    # 计算信号质量指标
    try:
        from scipy import interpolate
        # 确保插值范围正确
        min_time = max(min(time_points), min(ideal_time))
        max_time = min(max(time_points), max(ideal_time))

        # 创建在有效范围内的时间点
        valid_ideal_time = ideal_time[(ideal_time >= min_time) & (ideal_time <= max_time)]
        valid_ideal_signal = [1000 * math.sin(2 * math.pi * 1.0 * t) for t in valid_ideal_time]

        f_interp = interpolate.interp1d(time_points, signal_values, kind='linear')
        interpolated_signal = f_interp(valid_ideal_time)

        # 计算均方根误差
        rmse = np.sqrt(np.mean((np.array(valid_ideal_signal) - interpolated_signal)**2))
        print(f"\n=== 信号质量 ===")
        print(f"与理想信号的RMSE: {rmse:.2f}")
    except ImportError:
        print(f"\n=== 信号质量 ===")
        print("需要scipy库进行详细分析")
    
    return time_points, signal_values

def demonstrate_smoothing_effect():
    """演示平滑效果的改进"""
    print("生成测试数据并显示对比图...")
    time_points, signal_values = plot_comparison()
    
    print("\n=== 改进说明 ===")
    print("1. 旧方法问题：")
    print("   - 按采样点索引绘图，忽略了真实的时间信息")
    print("   - 不均匀的采样会导致信号看起来有锯齿")
    print("   - 无法反映真实的信号频率和相位")
    
    print("\n2. 新方法改进：")
    print("   - 使用真实的时间轴进行绘图")
    print("   - 正确反映信号的时间特性")
    print("   - 更平滑的视觉效果")
    print("   - 便于分析信号的频域特性")
    
    print("\n3. 实际应用效果：")
    print("   - 正弦波信号会显示为平滑的曲线")
    print("   - 阶跃信号的时间点更准确")
    print("   - 便于观察信号的真实特性")

if __name__ == "__main__":
    try:
        demonstrate_smoothing_effect()
    except ImportError as e:
        if 'scipy' in str(e):
            print("注意：需要安装scipy库来运行完整的分析")
            print("可以运行：pip install scipy")
            # 不使用scipy的简化版本
            time_points, signal_values = generate_test_data()
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # 旧方法
            ax1.plot(signal_values, 'b-o', markersize=3)
            ax1.set_title('旧方法：按采样点索引')
            ax1.set_xlabel('采样点索引')
            ax1.set_ylabel('信号值')
            ax1.grid(True)
            
            # 新方法
            ax2.plot(time_points, signal_values, 'r-o', markersize=3)
            ax2.set_title('新方法：按真实时间轴')
            ax2.set_xlabel('时间 (秒)')
            ax2.set_ylabel('信号值')
            ax2.grid(True)
            
            plt.tight_layout()
            plt.show()
        else:
            raise
